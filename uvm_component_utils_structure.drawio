<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="uvm_component_utils_structure" id="uvm_component_utils_structure">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="uvm_component_utils 宏定义层次结构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="400" height="30" as="geometry" />
        </mxCell>
        
        <!-- 基础宏 uvm_component_utils -->
        <mxCell id="main_macro" value="🔧 uvm_component_utils(T)&lt;br&gt;基础组件工具宏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;strokeWidth=3;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="80" width="250" height="60" as="geometry" />
        </mxCell>
        
        <!-- 工厂注册宏 -->
        <mxCell id="registry_macro" value="📋 m_uvm_component_registry_internal(T,T)&lt;br&gt;工厂注册功能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="200" y="200" width="280" height="50" as="geometry" />
        </mxCell>
        
        <!-- 类型名称宏 -->
        <mxCell id="typename_macro" value="🏷️ m_uvm_get_type_name_func(T)&lt;br&gt;类型名称功能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;strokeWidth=2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="650" y="200" width="280" height="50" as="geometry" />
        </mxCell>
        
        <!-- 工厂注册实现 -->
        <mxCell id="typedef_impl" value="📝 typedef uvm_component_registry #(T,&quot;T&quot;) type_id;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="300" width="150" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="get_type_impl" value="⚡ static function type_id get_type();&lt;br&gt;return type_id::get();&lt;br&gt;endfunction" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="220" y="300" width="150" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="get_obj_type_impl" value="🔄 virtual function uvm_object_wrapper get_object_type();&lt;br&gt;return type_id::get();&lt;br&gt;endfunction" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="390" y="300" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 类型名称实现 -->
        <mxCell id="type_name_const" value="📄 const static string type_name = &quot;T&quot;;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="650" y="300" width="150" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="get_type_name_impl" value="📖 virtual function string get_type_name();&lt;br&gt;return type_name;&lt;br&gt;endfunction" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="820" y="300" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 扩展版本 -->
        <mxCell id="begin_macro" value="🔧+ uvm_component_utils_begin(T)&lt;br&gt;支持字段宏的版本" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="450" width="280" height="50" as="geometry" />
        </mxCell>
        
        <!-- 字段工具宏 -->
        <mxCell id="field_utils_macro" value="🛠️ uvm_field_utils_begin(T)&lt;br&gt;字段自动化功能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;strokeWidth=2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="250" height="50" as="geometry" />
        </mxCell>
        
        <!-- 字段自动化实现 -->
        <mxCell id="field_automation" value="⚙️ function void __m_uvm_field_automation(...)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="550" width="250" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="var_decl" value="📊 变量声明&lt;br&gt;T local_data__;&lt;br&gt;typedef T ___local_type____;&lt;br&gt;string string_aa_key;&lt;br&gt;uvm_object __current_scopes[$];" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="250" y="620" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="logic_impl" value="🔄 处理逻辑&lt;br&gt;循环检查&lt;br&gt;super调用&lt;br&gt;类型转换" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="450" y="620" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 结束宏 -->
        <mxCell id="end_macro" value="🔚 uvm_component_utils_end&lt;br&gt;结束字段宏块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;strokeWidth=2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="620" width="200" height="50" as="geometry" />
        </mxCell>
        
        <!-- 参数化版本 -->
        <mxCell id="param_macro" value="🔧* uvm_component_param_utils(T)&lt;br&gt;参数化组件宏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="250" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="param_registry" value="📋* m_uvm_component_registry_param(T)&lt;br&gt;参数化工厂注册(无字符串名称)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="850" y="550" width="250" height="40" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" edge="1" parent="1" source="main_macro" target="registry_macro">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="main_macro" target="typename_macro">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="registry_macro" target="typedef_impl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="registry_macro" target="get_type_impl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="registry_macro" target="get_obj_type_impl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="typename_macro" target="type_name_const">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="typename_macro" target="get_type_name_impl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="begin_macro" target="main_macro">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="begin_macro" target="field_utils_macro">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="field_utils_macro" target="field_automation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge11" edge="1" parent="1" source="field_automation" target="var_decl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" edge="1" parent="1" source="field_automation" target="logic_impl">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" edge="1" parent="1" source="param_macro" target="param_registry">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="750" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend1" value="🔧 基础宏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="780" width="80" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend2" value="📋 子宏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="150" y="780" width="80" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend3" value="📝 实现代码" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="250" y="780" width="80" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend4" value="🔧+ 扩展版本" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="350" y="780" width="80" height="25" as="geometry" />
        </mxCell>
        
        <mxCell id="legend5" value="🔧* 参数化版本" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="780" width="90" height="25" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
